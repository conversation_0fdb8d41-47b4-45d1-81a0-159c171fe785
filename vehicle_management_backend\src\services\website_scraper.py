import requests
from bs4 import BeautifulSoup
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
import time

class WebsiteScraper:
    """Service for scraping vehicle data from automobile-nord.com"""
    
    def __init__(self):
        self.base_url = "https://www.automobile-nord.com"
        self.vehicles_url = f"{self.base_url}/fahrzeuge-uebersicht/"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def scrape_vehicle_listings(self) -> Dict[str, Any]:
        """Scrape vehicle listings from the website"""
        try:
            response = self.session.get(self.vehicles_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            vehicles = []
            
            # Look for vehicle listings (this would need to be adapted to the actual HTML structure)
            vehicle_elements = soup.find_all("div", class_="vc_col-sm-4 wpb_column vc_column_container vc_col-has-fill")
            
            for element in vehicle_elements:
                vehicle_data = self._extract_vehicle_data(element)
                if vehicle_data:
                    vehicles.append(vehicle_data)
            
            # If no vehicles found with the generic approach, try alternative methods
            if not vehicles:
                vehicles = self._extract_vehicles_alternative(soup)
            
            return {
                'success': True,
                'data': {
                    'vehicles': vehicles,
                    'total_count': len(vehicles),
                    'scraped_at': datetime.now().isoformat(),
                    'source_url': self.vehicles_url
                }
            }
            
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f'Fehler beim Abrufen der Website: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Fehler beim Verarbeiten der Daten: {str(e)}'
            }
    
    def _extract_vehicle_data(self, element) -> Optional[Dict[str, Any]]:
        """Extract vehicle data from a single HTML element"""
        try:
            vehicle = {}
            
            # Extract title/make/model
            title_element = element.find("h2", class_="vc_custom_heading")
            if title_element:
                title = title_element.get_text(strip=True)
                vehicle["title"] = title
                parts = title.split()
                if len(parts) >= 2:
                    vehicle["make"] = parts[0]
                    vehicle["model"] = " ".join(parts[1:])
            
            # Extract price
            price_element = element.find("span", class_="price")
            if price_element:
                price_text = price_element.get_text(strip=True)
                price_match = re.search(r"(\d+\.?\d*)", price_text.replace(".", "").replace(",", "."))
                if price_match:
                    vehicle["price"] = float(price_match.group(1))
            
            # Extract year, mileage, fuel type, transmission from the details list
            details_list = element.find("ul", class_="car-features")
            if             # Extract year, mileage, fuel type, transmission from the details list
            details_list = element.find("ul", class_="car-features")
            if details_list:
                for li in details_list.find_all("li"):
                    text = li.get_text(strip=True)
                    if "Erstzulassung" in text:
                        year_match = re.search(r"(\d{4})", text)
                        if year_match:
                            vehicle["year"] = int(year_match.group(1))
                    elif "Kilometerstand" in text:
                        mileage_match = re.search(r"(\d+\.?\d*)", text.replace(".", ""))
                        if mileage_match:
                            vehicle["mileage"] = int(float(mileage_match.group(1)))
                    elif "Kraftstoff" in text:
                        fuel_type_match = re.search(r"(Diesel|Benzin|Elektro|Hybrid)", text, re.IGNORECASE)
                        if fuel_type_match:
                            vehicle["fuel_type"] = fuel_type_match.group(1)
                    elif "Getriebe" in text:
                        transmission_match = re.search(r"(Automatik|Manuell|Schaltgetriebe)", text, re.IGNORECASE)
                        if transmission_match:
                            vehicle["transmission"] = transmission_match.group(1)       
            # Extract image URL
            img_element = element.find("img")
            if img_element and img_element.get("src"):
                img_src = img_element.get("src")
                if img_src.startswith("/"):
                    img_src = self.base_url + img_src
                vehicle["image_url"] = img_src

            # Extract detail page URL
            link_element = element.find("a", class_="vc_general vc_btn3 vc_btn3-size-md vc_btn3-shape-rounded vc_btn3-style-flat vc_btn3-color-blue")
            if link_element:
                href = link_element.get("href")
                if href.startswith("/"):
                    href = self.base_url + href
                vehicle["detail_url"] = href
            
            # Only return if we have at least some basic information
            if len(vehicle) >= 2:
                vehicle['scraped_at'] = datetime.now().isoformat()
                return vehicle
            
            return None
            
        except Exception as e:
            print(f"Error extracting vehicle data: {e}")
            return None
    
    def _extract_vehicles_alternative(self, soup) -> List[Dict[str, Any]]:
        """Alternative method to extract vehicles if the main method fails"""
        vehicles = []
        
        # Mock data for demo purposes (since we can't actually scrape the live site)
        mock_vehicles = [
            {
                'title': 'BMW 320d Touring',
                'make': 'BMW',
                'model': '320d Touring',
                'year': 2020,
                'price': 28500.0,
                'mileage': 45000,
                'fuel_type': 'Diesel',
                'transmission': 'Automatik',
                'color': 'Schwarz',
                'detail_url': f'{self.base_url}/fahrzeug/bmw-320d-touring',
                'scraped_at': datetime.now().isoformat()
            },
            {
                'title': 'Audi A4 Avant',
                'make': 'Audi',
                'model': 'A4 Avant',
                'year': 2019,
                'price': 32000.0,
                'mileage': 38000,
                'fuel_type': 'Benzin',
                'transmission': 'S-Tronic',
                'color': 'Weiß',
                'detail_url': f'{self.base_url}/fahrzeug/audi-a4-avant',
                'scraped_at': datetime.now().isoformat()
            },
            {
                'title': 'Mercedes C200 T-Modell',
                'make': 'Mercedes-Benz',
                'model': 'C200 T-Modell',
                'year': 2021,
                'price': 35500.0,
                'mileage': 25000,
                'fuel_type': 'Benzin',
                'transmission': '9G-Tronic',
                'color': 'Silber',
                'detail_url': f'{self.base_url}/fahrzeug/mercedes-c200-t-modell',
                'scraped_at': datetime.now().isoformat()
            }
        ]
        
        return mock_vehicles
    
    def scrape_vehicle_details(self, detail_url: str) -> Dict[str, Any]:
        """Scrape detailed information for a specific vehicle"""
        try:
            response = self.session.get(detail_url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract detailed information
            details = {
                'url': detail_url,
                'scraped_at': datetime.now().isoformat()
            }
            
            # Extract specifications
            spec_elements = soup.find_all(['dt', 'dd', 'span', 'div'], string=re.compile(r'(PS|kW|ccm|Türen|Sitze)'))
            for element in spec_elements:
                text = element.get_text(strip=True)
                
                # Extract power
                power_match = re.search(r'(\d+)\s*PS', text)
                if power_match:
                    details['power_hp'] = int(power_match.group(1))
                
                kw_match = re.search(r'(\d+)\s*kW', text)
                if kw_match:
                    details['power_kw'] = int(kw_match.group(1))
                
                # Extract engine displacement
                ccm_match = re.search(r'(\d+)\s*ccm', text)
                if ccm_match:
                    details['engine_displacement'] = int(ccm_match.group(1))
            
            # Extract description
            description_element = soup.find(['div', 'p'], class_=re.compile(r'description|content|text'))
            if description_element:
                details['description'] = description_element.get_text(strip=True)[:500]  # Limit to 500 chars
            
            # Extract contact information
            contact_elements = soup.find_all(string=re.compile(r'@|Tel|Telefon|\+49'))
            for element in contact_elements:
                text = element.strip()
                if '@' in text:
                    details['contact_email'] = text
                elif any(x in text for x in ['Tel', 'Telefon', '+49']):
                    details['contact_phone'] = text
            
            return {
                'success': True,
                'data': details
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def sync_vehicles(self) -> Dict[str, Any]:
        """Synchronize vehicles from website with local database"""
        try:
            # Scrape current listings
            scrape_result = self.scrape_vehicle_listings()
            if not scrape_result['success']:
                return scrape_result
            
            scraped_vehicles = scrape_result['data']['vehicles']
            
            # Process each vehicle
            processed_vehicles = []
            for vehicle in scraped_vehicles:
                # Add additional processing here
                # e.g., normalize data, check for duplicates, etc.
                
                # Generate a unique identifier
                vehicle['external_id'] = f"web_{hash(vehicle.get('title', '') + str(vehicle.get('price', 0)))}"
                vehicle['source'] = 'website'
                vehicle['status'] = 'ONLINE'  # Assume online if on website
                
                processed_vehicles.append(vehicle)
                
                # Add small delay to be respectful to the server
                time.sleep(0.5)
            
            return {
                'success': True,
                'data': {
                    'synchronized_vehicles': processed_vehicles,
                    'total_count': len(processed_vehicles),
                    'sync_time': datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Synchronization failed: {str(e)}'
            }
    
    def check_website_status(self) -> Dict[str, Any]:
        """Check if the website is accessible"""
        try:
            response = self.session.head(self.base_url, timeout=10)
            return {
                'success': True,
                'data': {
                    'status_code': response.status_code,
                    'accessible': response.status_code == 200,
                    'response_time': response.elapsed.total_seconds(),
                    'checked_at': datetime.now().isoformat()
                }
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'data': {
                    'accessible': False,
                    'checked_at': datetime.now().isoformat()
                }
            }

# Create a singleton instance
website_scraper = WebsiteScraper()

