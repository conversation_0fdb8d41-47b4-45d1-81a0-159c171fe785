from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from enum import Enum

db = SQLAlchemy()

class VehicleStatus(Enum):
    NEW_ARRIVAL = "new_arrival"
    IN_WORKSHOP = "in_workshop"
    IN_PREPARATION = "in_preparation"
    PHOTOGRAPHED = "photographed"
    ONLINE = "online"
    RESERVED = "reserved"
    SOLD = "sold"
    EXPORT_READY = "export_ready"

class Vehicle(db.Model):
    __tablename__ = 'vehicles'
    
    id = db.Column(db.Integer, primary_key=True)
    vin = db.Column(db.String(17), unique=True, nullable=False)
    make = db.Column(db.String(50), nullable=False)
    model = db.Column(db.String(100), nullable=False)
    year = db.Column(db.Integer, nullable=False)
    mileage = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)
    fuel_type = db.Column(db.String(20), nullable=False)
    transmission = db.Column(db.String(20), nullable=False)
    body_type = db.Column(db.String(30), nullable=False)
    color = db.Column(db.String(30), nullable=False)
    engine_size = db.Column(db.Float)
    power_kw = db.Column(db.Integer)
    power_hp = db.Column(db.Integer)
    doors = db.Column(db.Integer)
    seats = db.Column(db.Integer)
    description = db.Column(db.Text)
    features = db.Column(db.Text)  # JSON string of features
    status = db.Column(db.Enum(VehicleStatus), default=VehicleStatus.NEW_ARRIVAL)
    
    # External integration fields
    mobile_de_id = db.Column(db.String(50))
    last_sync_mobile_de = db.Column(db.DateTime)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    assigned_to = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # Relationships
    images = db.relationship('VehicleImage', backref='vehicle', lazy=True, cascade='all, delete-orphan')
    status_history = db.relationship('VehicleStatusHistory', backref='vehicle', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'vin': self.vin,
            'make': self.make,
            'model': self.model,
            'year': self.year,
            'mileage': self.mileage,
            'price': self.price,
            'fuel_type': self.fuel_type,
            'transmission': self.transmission,
            'body_type': self.body_type,
            'color': self.color,
            'engine_size': self.engine_size,
            'power_kw': self.power_kw,
            'power_hp': self.power_hp,
            'doors': self.doors,
            'seats': self.seats,
            'description': self.description,
            'features': self.features,
            'status': self.status.value if self.status else None,
            'mobile_de_id': self.mobile_de_id,
            'last_sync_mobile_de': self.last_sync_mobile_de.isoformat() if self.last_sync_mobile_de else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'assigned_to': self.assigned_to,
            'images': [img.to_dict() for img in self.images]
        }

class VehicleImage(db.Model):
    __tablename__ = 'vehicle_images'
    
    id = db.Column(db.Integer, primary_key=True)
    vehicle_id = db.Column(db.Integer, db.ForeignKey('vehicles.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255))
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)
    mime_type = db.Column(db.String(50))
    is_primary = db.Column(db.Boolean, default=False)
    sort_order = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'vehicle_id': self.vehicle_id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'mime_type': self.mime_type,
            'is_primary': self.is_primary,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class VehicleStatusHistory(db.Model):
    __tablename__ = 'vehicle_status_history'
    
    id = db.Column(db.Integer, primary_key=True)
    vehicle_id = db.Column(db.Integer, db.ForeignKey('vehicles.id'), nullable=False)
    old_status = db.Column(db.Enum(VehicleStatus))
    new_status = db.Column(db.Enum(VehicleStatus), nullable=False)
    changed_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    changed_at = db.Column(db.DateTime, default=datetime.utcnow)
    notes = db.Column(db.Text)
    
    def to_dict(self):
        return {
            'id': self.id,
            'vehicle_id': self.vehicle_id,
            'old_status': self.old_status.value if self.old_status else None,
            'new_status': self.new_status.value if self.new_status else None,
            'changed_by': self.changed_by,
            'changed_at': self.changed_at.isoformat() if self.changed_at else None,
            'notes': self.notes
        }

