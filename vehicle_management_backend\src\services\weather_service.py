import requests
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

class WeatherService:
    """Service for fetching weather data"""
    
    def __init__(self):
        # Using OpenWeatherMap API (free tier)
        # In production, you would get an API key from openweathermap.org
        self.api_key = "demo_key"  # Replace with actual API key
        self.base_url = "http://api.openweathermap.org/data/2.5"
        
    def get_current_weather(self, city: str = "Harrislee") -> Dict[str, Any]:
        """Get current weather for a city"""
        try:
            # For demo purposes, return mock data
            # In production, uncomment the API call below
            
            # url = f"{self.base_url}/weather"
            # params = {
            #     'q': city,
            #     'appid': self.api_key,
            #     'units': 'metric',
            #     'lang': 'de'
            # }
            # response = requests.get(url, params=params, timeout=10)
            # response.raise_for_status()
            # data = response.json()
            
            # Mock data for demo
            mock_data = {
                'weather': [{'main': 'Clear', 'description': 'Sonnig', 'icon': '01d'}],
                'main': {
                    'temp': 22.5,
                    'feels_like': 24.1,
                    'temp_min': 18.2,
                    'temp_max': 26.8,
                    'humidity': 65
                },
                'wind': {'speed': 3.2, 'deg': 180},
                'visibility': 10000,
                'name': city
            }
            
            return self._format_weather_data(mock_data)
            
        except requests.exceptions.RequestException as e:
            print(f"Weather API error: {e}")
            return self._get_fallback_weather()
    
    def get_weather_forecast(self, city: str = "Harrislee", days: int = 5) -> Dict[str, Any]:
        """Get weather forecast for multiple days"""
        try:
            # Mock forecast data for demo
            forecast_data = []
            base_temp = 22
            
            for i in range(days):
                date = datetime.now() + timedelta(days=i)
                temp_variation = (-2, 0, 3, -1, 2)[i % 5]
                
                forecast_data.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'day_name': date.strftime('%A'),
                    'temp_max': base_temp + temp_variation + 4,
                    'temp_min': base_temp + temp_variation - 3,
                    'description': ['Sonnig', 'Bewölkt', 'Regenschauer', 'Teilweise bewölkt', 'Sonnig'][i % 5],
                    'icon': ['01d', '03d', '10d', '02d', '01d'][i % 5],
                    'humidity': 65 + (i * 5),
                    'wind_speed': 3.2 + (i * 0.5)
                })
            
            return {
                'city': city,
                'forecast': forecast_data,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            print(f"Weather forecast error: {e}")
            return {'city': city, 'forecast': [], 'error': str(e)}
    
    def get_weather_alerts(self, city: str = "Harrislee") -> Dict[str, Any]:
        """Get weather alerts and warnings"""
        # Mock alerts for demo
        alerts = []
        
        # Simulate occasional weather warnings
        import random
        if random.random() < 0.3:  # 30% chance of alert
            alerts.append({
                'title': 'Regenwarnung',
                'description': 'Starker Regen erwartet zwischen 14:00 und 18:00 Uhr',
                'severity': 'moderate',
                'start_time': (datetime.now() + timedelta(hours=2)).isoformat(),
                'end_time': (datetime.now() + timedelta(hours=6)).isoformat()
            })
        
        return {
            'city': city,
            'alerts': alerts,
            'alert_count': len(alerts),
            'last_checked': datetime.now().isoformat()
        }
    
    def _format_weather_data(self, raw_data: Dict) -> Dict[str, Any]:
        """Format raw weather data for frontend consumption"""
        return {
            'temperature': round(raw_data['main']['temp'], 1),
            'feels_like': round(raw_data['main']['feels_like'], 1),
            'temp_min': round(raw_data['main']['temp_min'], 1),
            'temp_max': round(raw_data['main']['temp_max'], 1),
            'humidity': raw_data['main']['humidity'],
            'description': raw_data['weather'][0]['description'],
            'icon': raw_data['weather'][0]['icon'],
            'wind_speed': raw_data['wind']['speed'],
            'wind_direction': raw_data['wind']['deg'],
            'city': raw_data['name'],
            'last_updated': datetime.now().isoformat()
        }
    
    def _get_fallback_weather(self) -> Dict[str, Any]:
        """Return fallback weather data when API is unavailable"""
        return {
            'temperature': 20.0,
            'feels_like': 22.0,
            'temp_min': 16.0,
            'temp_max': 24.0,
            'humidity': 60,
            'description': 'Keine Wetterdaten verfügbar',
            'icon': '01d',
            'wind_speed': 2.0,
            'wind_direction': 180,
            'city': 'Hamburg',
            'last_updated': datetime.now().isoformat(),
            'error': 'Weather service unavailable'
        }

# Create a singleton instance
weather_service = WeatherService()

