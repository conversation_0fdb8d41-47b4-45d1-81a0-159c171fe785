from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
from enum import Enum

db = SQLAlchemy()

class UserRole(Enum):
    ADMINISTRATOR = "administrator"
    MANAGER = "manager"
    SALES_REPRESENTATIVE = "sales_representative"
    MECHANIC = "mechanic"
    PHOTOGRAPHER = "photographer"
    EMPLOYEE = "employee"

class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # Personal information
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    phone = db.Column(db.String(20))
    
    # Role and permissions
    role = db.Column(db.Enum(UserRole), default=UserRole.EMPLOYEE)
    is_active = db.Column(db.Boolean, default=True)
    is_admin = db.Column(db.Boolean, default=False)
    
    # Authentication tracking
    last_login = db.Column(db.DateTime)
    login_count = db.Column(db.Integer, default=0)
    failed_login_attempts = db.Column(db.Integer, default=0)
    account_locked_until = db.Column(db.DateTime)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))

    def __repr__(self):
        return f'<User {self.username}>'

    def set_password(self, password):
        """Set password hash"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)

    def get_full_name(self):
        """Get user's full name"""
        return f"{self.first_name} {self.last_name}"

    def is_account_locked(self):
        """Check if account is locked"""
        if self.account_locked_until:
            return datetime.utcnow() < self.account_locked_until
        return False

    def lock_account(self, duration_minutes=30):
        """Lock account for specified duration"""
        self.account_locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        self.updated_at = datetime.utcnow()

    def unlock_account(self):
        """Unlock account"""
        self.account_locked_until = None
        self.failed_login_attempts = 0
        self.updated_at = datetime.utcnow()

    def record_login(self):
        """Record successful login"""
        self.last_login = datetime.utcnow()
        self.login_count += 1
        self.failed_login_attempts = 0
        self.updated_at = datetime.utcnow()

    def record_failed_login(self):
        """Record failed login attempt"""
        self.failed_login_attempts += 1
        self.updated_at = datetime.utcnow()
        
        # Lock account after 5 failed attempts
        if self.failed_login_attempts >= 5:
            self.lock_account()

    def has_permission(self, permission):
        """Check if user has specific permission"""
        if self.is_admin:
            return True
        
        # Define role-based permissions
        permissions = {
            UserRole.ADMINISTRATOR: ['*'],
            UserRole.MANAGER: [
                'vehicle:read', 'vehicle:create', 'vehicle:update', 'vehicle:delete',
                'task:read', 'task:create', 'task:update', 'task:delete',
                'user:read', 'user:update', 'report:read'
            ],
            UserRole.SALES_REPRESENTATIVE: [
                'vehicle:read', 'vehicle:update',
                'task:read', 'task:create', 'task:update',
                'customer:read', 'customer:create', 'customer:update'
            ],
            UserRole.MECHANIC: [
                'vehicle:read', 'task:read', 'task:update',
                'maintenance:read', 'maintenance:create', 'maintenance:update'
            ],
            UserRole.PHOTOGRAPHER: [
                'vehicle:read', 'task:read', 'task:update',
                'media:read', 'media:create', 'media:update', 'media:delete'
            ],
            UserRole.EMPLOYEE: [
                'vehicle:read', 'task:read'
            ]
        }
        
        user_permissions = permissions.get(self.role, [])
        return '*' in user_permissions or permission in user_permissions

    def to_dict(self, include_sensitive=False):
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.get_full_name(),
            'phone': self.phone,
            'role': self.role.value if self.role else None,
            'is_active': self.is_active,
            'is_admin': self.is_admin,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_sensitive:
            data.update({
                'last_login': self.last_login.isoformat() if self.last_login else None,
                'login_count': self.login_count,
                'failed_login_attempts': self.failed_login_attempts,
                'is_account_locked': self.is_account_locked()
            })
        
        return data
