import re
import requests
from typing import Dict, Any, Optional
from datetime import datetime

class VINScanner:
    """Service for VIN (Vehicle Identification Number) scanning and validation"""
    
    def __init__(self):
        # VIN validation pattern (17 characters, no I, O, Q)
        self.vin_pattern = re.compile(r'^[A-HJ-NPR-Z0-9]{17}$')
        
        # Mock VIN database for demo purposes
        self.vin_database = {
            'WBAVA31070NL12345': {
                'make': 'BMW',
                'model': '320d',
                'year': 2020,
                'engine': '2.0L Diesel',
                'transmission': 'Automatik',
                'fuel_type': 'Diesel',
                'color': 'Schwarz',
                'country': 'Deutschland'
            },
            'WAUZZZ8V7KA123456': {
                'make': 'Audi',
                'model': 'A4',
                'year': 2019,
                'engine': '2.0L TFSI',
                'transmission': 'S-Tronic',
                'fuel_type': 'Benzin',
                'color': 'Weiß',
                'country': 'Deutschland'
            },
            'WDD2050421A123789': {
                'make': 'Mercedes-Benz',
                'model': 'C200',
                'year': 2021,
                'engine': '1.5L Turbo',
                'transmission': '9G-Tronic',
                'fuel_type': 'Benzin',
                'color': 'Silber',
                'country': 'Deutschland'
            }
        }
    
    def validate_vin(self, vin: str) -> Dict[str, Any]:
        """Validate VIN format and checksum"""
        if not vin:
            return {'valid': False, 'error': 'VIN ist leer'}
        
        vin = vin.upper().strip()
        
        # Check length
        if len(vin) != 17:
            return {'valid': False, 'error': f'VIN muss 17 Zeichen haben, gefunden: {len(vin)}'}
        
        # Check pattern
        if not self.vin_pattern.match(vin):
            return {'valid': False, 'error': 'VIN enthält ungültige Zeichen (I, O, Q sind nicht erlaubt)'}
        
        # Basic checksum validation (simplified)
        if self._validate_checksum(vin):
            return {'valid': True, 'vin': vin}
        else:
            return {'valid': False, 'error': 'VIN Prüfsumme ist ungültig'}
    
    def decode_vin(self, vin: str) -> Dict[str, Any]:
        """Decode VIN to extract vehicle information"""
        validation = self.validate_vin(vin)
        if not validation['valid']:
            return validation
        
        vin = validation['vin']
        
        # Check mock database first
        if vin in self.vin_database:
            vehicle_data = self.vin_database[vin].copy()
            vehicle_data['vin'] = vin
            vehicle_data['source'] = 'database'
            vehicle_data['decoded_at'] = datetime.now().isoformat()
            return {'success': True, 'data': vehicle_data}
        
        # Decode basic information from VIN structure
        decoded_info = self._decode_vin_structure(vin)
        decoded_info['vin'] = vin
        decoded_info['source'] = 'decoded'
        decoded_info['decoded_at'] = datetime.now().isoformat()
        
        return {'success': True, 'data': decoded_info}
    
    def scan_vin_from_image(self, image_data: bytes) -> Dict[str, Any]:
        """Simulate VIN scanning from camera image"""
        # In a real implementation, this would use OCR (like Tesseract)
        # to extract VIN from the image
        
        # For demo purposes, simulate successful scan
        demo_vins = list(self.vin_database.keys())
        import random
        
        if random.random() < 0.8:  # 80% success rate
            scanned_vin = random.choice(demo_vins)
            return {
                'success': True,
                'vin': scanned_vin,
                'confidence': 0.95,
                'scan_time': datetime.now().isoformat()
            }
        else:
            return {
                'success': False,
                'error': 'VIN konnte nicht erkannt werden. Bitte versuchen Sie es erneut.',
                'scan_time': datetime.now().isoformat()
            }
    
    def get_vehicle_history(self, vin: str) -> Dict[str, Any]:
        """Get vehicle history based on VIN"""
        validation = self.validate_vin(vin)
        if not validation['valid']:
            return validation
        
        # Mock history data
        history = {
            'vin': vin,
            'registration_history': [
                {
                    'date': '2020-03-15',
                    'event': 'Erstzulassung',
                    'location': 'Hamburg, Deutschland'
                }
            ],
            'service_history': [
                {
                    'date': '2021-03-20',
                    'mileage': 15000,
                    'service_type': 'Inspektion',
                    'location': 'BMW Service Hamburg'
                },
                {
                    'date': '2022-03-18',
                    'mileage': 28000,
                    'service_type': 'Inspektion',
                    'location': 'BMW Service Hamburg'
                }
            ],
            'accidents': [],
            'recalls': [],
            'last_updated': datetime.now().isoformat()
        }
        
        return {'success': True, 'data': history}
    
    def _validate_checksum(self, vin: str) -> bool:
        """Validate VIN checksum (simplified implementation)"""
        # This is a simplified checksum validation
        # Real VIN validation is more complex
        weights = [8, 7, 6, 5, 4, 3, 2, 10, 0, 9, 8, 7, 6, 5, 4, 3, 2]
        values = {'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5, 'F': 6, 'G': 7, 'H': 8,
                 'J': 1, 'K': 2, 'L': 3, 'M': 4, 'N': 5, 'P': 7, 'R': 9,
                 'S': 2, 'T': 3, 'U': 4, 'V': 5, 'W': 6, 'X': 7, 'Y': 8, 'Z': 9}
        
        try:
            total = 0
            for i, char in enumerate(vin):
                if char.isdigit():
                    total += int(char) * weights[i]
                else:
                    total += values.get(char, 0) * weights[i]
            
            check_digit = total % 11
            if check_digit == 10:
                check_digit = 'X'
            else:
                check_digit = str(check_digit)
            
            return str(vin[8]) == check_digit
        except:
            return True  # Return True for demo purposes
    
    def _decode_vin_structure(self, vin: str) -> Dict[str, Any]:
        """Decode basic VIN structure"""
        # World Manufacturer Identifier (positions 1-3)
        wmi = vin[:3]
        
        # Vehicle Descriptor Section (positions 4-9)
        vds = vin[3:9]
        
        # Vehicle Identifier Section (positions 10-17)
        vis = vin[9:]
        
        # Model year (position 10)
        year_code = vin[9]
        year = self._decode_year(year_code)
        
        # Basic manufacturer detection
        manufacturer = self._detect_manufacturer(wmi)
        
        return {
            'wmi': wmi,
            'vds': vds,
            'vis': vis,
            'make': manufacturer,
            'model': 'Unbekannt',
            'year': year,
            'engine': 'Unbekannt',
            'transmission': 'Unbekannt',
            'fuel_type': 'Unbekannt',
            'color': 'Unbekannt',
            'country': self._detect_country(wmi)
        }
    
    def _decode_year(self, year_code: str) -> int:
        """Decode model year from VIN"""
        year_map = {
            'A': 2010, 'B': 2011, 'C': 2012, 'D': 2013, 'E': 2014,
            'F': 2015, 'G': 2016, 'H': 2017, 'J': 2018, 'K': 2019,
            'L': 2020, 'M': 2021, 'N': 2022, 'P': 2023, 'R': 2024
        }
        return year_map.get(year_code, 2020)
    
    def _detect_manufacturer(self, wmi: str) -> str:
        """Detect manufacturer from WMI"""
        manufacturer_map = {
            'WBA': 'BMW', 'WBS': 'BMW', 'WBY': 'BMW',
            'WAU': 'Audi', 'WA1': 'Audi',
            'WDD': 'Mercedes-Benz', 'WDC': 'Mercedes-Benz',
            'WVW': 'Volkswagen', 'WV1': 'Volkswagen',
            'WP0': 'Porsche'
        }
        return manufacturer_map.get(wmi, 'Unbekannt')
    
    def _detect_country(self, wmi: str) -> str:
        """Detect country from WMI"""
        if wmi.startswith('W'):
            return 'Deutschland'
        elif wmi.startswith('1') or wmi.startswith('4') or wmi.startswith('5'):
            return 'USA'
        elif wmi.startswith('J'):
            return 'Japan'
        else:
            return 'Unbekannt'

# Create a singleton instance
vin_scanner = VINScanner()

