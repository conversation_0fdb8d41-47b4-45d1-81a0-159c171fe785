from flask import Blueprint, jsonify, request
from ..services.weather_service import weather_service

weather_bp = Blueprint('weather', __name__)

@weather_bp.route('/weather/current', methods=['GET'])
def get_current_weather():
    """Get current weather data"""
    try:
        city = request.args.get('city', 'Hamburg')
        weather_data = weather_service.get_current_weather(city)
        return jsonify({
            'success': True,
            'data': weather_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@weather_bp.route('/weather/forecast', methods=['GET'])
def get_weather_forecast():
    """Get weather forecast"""
    try:
        city = request.args.get('city', 'Hamburg')
        days = int(request.args.get('days', 5))
        
        if days > 7:
            days = 7  # Limit to 7 days
        
        forecast_data = weather_service.get_weather_forecast(city, days)
        return jsonify({
            'success': True,
            'data': forecast_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@weather_bp.route('/weather/alerts', methods=['GET'])
def get_weather_alerts():
    """Get weather alerts and warnings"""
    try:
        city = request.args.get('city', 'Hamburg')
        alerts_data = weather_service.get_weather_alerts(city)
        return jsonify({
            'success': True,
            'data': alerts_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@weather_bp.route('/weather/summary', methods=['GET'])
def get_weather_summary():
    """Get comprehensive weather summary"""
    try:
        city = request.args.get('city', 'Hamburg')
        
        current = weather_service.get_current_weather(city)
        forecast = weather_service.get_weather_forecast(city, 3)
        alerts = weather_service.get_weather_alerts(city)
        
        return jsonify({
            'success': True,
            'data': {
                'current': current,
                'forecast': forecast['forecast'][:3],  # Next 3 days
                'alerts': alerts['alerts'],
                'city': city
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

