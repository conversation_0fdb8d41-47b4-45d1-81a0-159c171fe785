import os
import sys
# DON\'T CHANGE THIS !!!
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from flask import Flask, send_from_directory
from flask_cors import CORS
from src.models.user import db

def create_app():
    app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))
    app.config['SECRET_KEY'] = 'asdf#FGSgvasgf$5$WGT'

    # Enable CORS for all routes
    CORS(app)

    # Register blueprints
    from src.routes.user import user_bp
    from src.routes.vehicle import vehicle_bp
    from src.routes.task import task_bp
    from src.routes.weather import weather_bp
    from src.routes.vin import vin_bp
    app.register_blueprint(user_bp, url_prefix='/api')
    app.register_blueprint(vehicle_bp, url_prefix='/api')
    app.register_blueprint(task_bp, url_prefix='/api')
    app.register_blueprint(weather_bp, url_prefix='/api')
    app.register_blueprint(vin_bp, url_prefix='/api')

    # Database configuration
    app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(os.path.dirname(__file__), 'database', 'app.db')}"
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)

    # Create tables and default admin user
    with app.app_context():
        db.create_all()
        
        # Create default admin user if not exists
        from src.models.user import User, UserRole
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                first_name='Admin',
                last_name='User',
                role=UserRole.ADMINISTRATOR,
                is_admin=True
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("Default admin user created: admin/admin123")
    return app

app = create_app()

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    static_folder_path = app.static_folder
    if static_folder_path is None:
            return "Static folder not configured", 404

    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404

@app.route('/api/health', methods=['GET'])
def health_check():
    return {'status': 'healthy', 'message': 'Vehicle Management API is running'}

@app.route('/api/scrape/sync', methods=['POST'])
def sync_website_data():
    """Sync vehicle data from website"""
    try:
        from src.services.website_scraper import website_scraper
        result = website_scraper.sync_vehicles()
        return result
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }, 500

@app.route('/api/scrape/status', methods=['GET'])
def check_website_status():
    """Check website accessibility"""
    try:
        from src.services.website_scraper import website_scraper
        result = website_scraper.check_website_status()
        return result
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }, 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)


