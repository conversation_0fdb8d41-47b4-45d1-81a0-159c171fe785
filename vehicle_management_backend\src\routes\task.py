from flask import Blueprint, request, jsonify
from src.models.task import Task, TaskStatus, TaskType, TaskPriority, db
from src.models.user import User
from src.models.vehicle import Vehicle
from datetime import datetime, timedelta
import json

task_bp = Blueprint('task', __name__)

@task_bp.route('/tasks', methods=['GET'])
def get_tasks():
    """Get all tasks with optional filtering"""
    try:
        # Get query parameters for filtering
        status = request.args.get('status')
        task_type = request.args.get('type')
        priority = request.args.get('priority')
        assigned_to = request.args.get('assigned_to', type=int)
        vehicle_id = request.args.get('vehicle_id', type=int)
        due_date_from = request.args.get('due_date_from')
        due_date_to = request.args.get('due_date_to')
        
        # Pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # Build query
        query = Task.query
        
        if status:
            try:
                status_enum = TaskStatus(status)
                query = query.filter(Task.status == status_enum)
            except ValueError:
                return jsonify({'error': 'Invalid status value'}), 400
                
        if task_type:
            try:
                type_enum = TaskType(task_type)
                query = query.filter(Task.task_type == type_enum)
            except ValueError:
                return jsonify({'error': 'Invalid task type value'}), 400
                
        if priority:
            try:
                priority_enum = TaskPriority(priority)
                query = query.filter(Task.priority == priority_enum)
            except ValueError:
                return jsonify({'error': 'Invalid priority value'}), 400
                
        if assigned_to:
            query = query.filter(Task.assigned_to == assigned_to)
            
        if vehicle_id:
            query = query.filter(Task.vehicle_id == vehicle_id)
            
        if due_date_from:
            try:
                from_date = datetime.fromisoformat(due_date_from)
                query = query.filter(Task.due_date >= from_date)
            except ValueError:
                return jsonify({'error': 'Invalid due_date_from format'}), 400
                
        if due_date_to:
            try:
                to_date = datetime.fromisoformat(due_date_to)
                query = query.filter(Task.due_date <= to_date)
            except ValueError:
                return jsonify({'error': 'Invalid due_date_to format'}), 400
        
        # Execute query with pagination
        tasks = query.order_by(Task.created_at.desc()).paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        return jsonify({
            'tasks': [task.to_dict() for task in tasks.items],
            'pagination': {
                'page': tasks.page,
                'pages': tasks.pages,
                'per_page': tasks.per_page,
                'total': tasks.total,
                'has_next': tasks.has_next,
                'has_prev': tasks.has_prev
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@task_bp.route('/tasks/<int:task_id>', methods=['GET'])
def get_task(task_id):
    """Get a specific task by ID"""
    try:
        task = Task.query.get_or_404(task_id)
        return jsonify(task.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@task_bp.route('/tasks', methods=['POST'])
def create_task():
    """Create a new task"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['title', 'task_type']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Validate enums
        try:
            task_type = TaskType(data['task_type'])
        except ValueError:
            return jsonify({'error': 'Invalid task type'}), 400
            
        priority = TaskPriority.MEDIUM  # Default priority
        if 'priority' in data:
            try:
                priority = TaskPriority(data['priority'])
            except ValueError:
                return jsonify({'error': 'Invalid priority'}), 400
        
        # Parse due date if provided
        due_date = None
        if 'due_date' in data:
            try:
                due_date = datetime.fromisoformat(data['due_date'])
            except ValueError:
                return jsonify({'error': 'Invalid due_date format'}), 400
        
        # Create new task
        task = Task(
            title=data['title'],
            description=data.get('description'),
            task_type=task_type,
            priority=priority,
            due_date=due_date,
            estimated_duration=data.get('estimated_duration'),
            vehicle_id=data.get('vehicle_id'),
            assigned_to=data.get('assigned_to'),
            created_by=data.get('created_by'),
            instructions=data.get('instructions'),
            required_tools=json.dumps(data.get('required_tools', [])),
            completion_notes=data.get('completion_notes')
        )
        
        db.session.add(task)
        db.session.commit()
        
        return jsonify(task.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@task_bp.route('/tasks/<int:task_id>', methods=['PUT'])
def update_task(task_id):
    """Update a task"""
    try:
        task = Task.query.get_or_404(task_id)
        data = request.get_json()
        
        # Update basic fields
        updatable_fields = [
            'title', 'description', 'estimated_duration', 'vehicle_id',
            'assigned_to', 'instructions', 'completion_notes'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(task, field, data[field])
        
        # Handle enum fields
        if 'task_type' in data:
            try:
                task.task_type = TaskType(data['task_type'])
            except ValueError:
                return jsonify({'error': 'Invalid task type'}), 400
                
        if 'priority' in data:
            try:
                task.priority = TaskPriority(data['priority'])
            except ValueError:
                return jsonify({'error': 'Invalid priority'}), 400
                
        if 'status' in data:
            try:
                old_status = task.status
                new_status = TaskStatus(data['status'])
                task.status = new_status
                
                # Update timestamps based on status change
                if new_status == TaskStatus.IN_PROGRESS and old_status != TaskStatus.IN_PROGRESS:
                    task.started_at = datetime.utcnow()
                elif new_status == TaskStatus.COMPLETED and old_status != TaskStatus.COMPLETED:
                    task.completed_at = datetime.utcnow()
                elif new_status == TaskStatus.CANCELLED and old_status != TaskStatus.CANCELLED:
                    task.cancelled_at = datetime.utcnow()
                    
            except ValueError:
                return jsonify({'error': 'Invalid status'}), 400
        
        # Handle due date
        if 'due_date' in data:
            if data['due_date']:
                try:
                    task.due_date = datetime.fromisoformat(data['due_date'])
                except ValueError:
                    return jsonify({'error': 'Invalid due_date format'}), 400
            else:
                task.due_date = None
        
        # Handle required tools as JSON
        if 'required_tools' in data:
            task.required_tools = json.dumps(data['required_tools'])
        
        task.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify(task.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@task_bp.route('/tasks/<int:task_id>', methods=['DELETE'])
def delete_task(task_id):
    """Delete a task"""
    try:
        task = Task.query.get_or_404(task_id)
        db.session.delete(task)
        db.session.commit()
        
        return jsonify({'message': 'Task deleted successfully'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@task_bp.route('/tasks/<int:task_id>/start', methods=['POST'])
def start_task(task_id):
    """Start a task"""
    try:
        task = Task.query.get_or_404(task_id)
        
        if task.status != TaskStatus.PENDING:
            return jsonify({'error': 'Task can only be started from pending status'}), 400
        
        task.status = TaskStatus.IN_PROGRESS
        task.started_at = datetime.utcnow()
        task.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({
            'message': 'Task started successfully',
            'task': task.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@task_bp.route('/tasks/<int:task_id>/complete', methods=['POST'])
def complete_task(task_id):
    """Complete a task"""
    try:
        task = Task.query.get_or_404(task_id)
        data = request.get_json() or {}
        
        if task.status not in [TaskStatus.PENDING, TaskStatus.IN_PROGRESS]:
            return jsonify({'error': 'Task can only be completed from pending or in-progress status'}), 400
        
        task.status = TaskStatus.COMPLETED
        task.completed_at = datetime.utcnow()
        task.updated_at = datetime.utcnow()
        
        # Update completion notes if provided
        if 'completion_notes' in data:
            task.completion_notes = data['completion_notes']
        
        # Update actual duration if provided
        if 'actual_duration' in data:
            task.actual_duration = data['actual_duration']
        
        db.session.commit()
        
        return jsonify({
            'message': 'Task completed successfully',
            'task': task.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@task_bp.route('/tasks/<int:task_id>/cancel', methods=['POST'])
def cancel_task(task_id):
    """Cancel a task"""
    try:
        task = Task.query.get_or_404(task_id)
        data = request.get_json() or {}
        
        if task.status == TaskStatus.COMPLETED:
            return jsonify({'error': 'Cannot cancel a completed task'}), 400
        
        task.status = TaskStatus.CANCELLED
        task.cancelled_at = datetime.utcnow()
        task.updated_at = datetime.utcnow()
        
        # Update cancellation reason if provided
        if 'cancellation_reason' in data:
            task.completion_notes = f"Cancelled: {data['cancellation_reason']}"
        
        db.session.commit()
        
        return jsonify({
            'message': 'Task cancelled successfully',
            'task': task.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@task_bp.route('/tasks/overdue', methods=['GET'])
def get_overdue_tasks():
    """Get overdue tasks"""
    try:
        now = datetime.utcnow()
        overdue_tasks = Task.query.filter(
            Task.due_date < now,
            Task.status.in_([TaskStatus.PENDING, TaskStatus.IN_PROGRESS])
        ).order_by(Task.due_date.asc()).all()
        
        return jsonify({
            'overdue_tasks': [task.to_dict() for task in overdue_tasks],
            'count': len(overdue_tasks)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@task_bp.route('/tasks/stats', methods=['GET'])
def get_task_stats():
    """Get task statistics"""
    try:
        # Count by status
        status_counts = {}
        for status in TaskStatus:
            count = Task.query.filter_by(status=status).count()
            status_counts[status.value] = count
        
        # Overdue tasks
        now = datetime.utcnow()
        overdue_count = Task.query.filter(
            Task.due_date < now,
            Task.status.in_([TaskStatus.PENDING, TaskStatus.IN_PROGRESS])
        ).count()
        
        # Tasks due today
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timedelta(days=1)
        due_today_count = Task.query.filter(
            Task.due_date >= today_start,
            Task.due_date < today_end,
            Task.status.in_([TaskStatus.PENDING, TaskStatus.IN_PROGRESS])
        ).count()
        
        # Tasks by priority
        priority_counts = {}
        for priority in TaskPriority:
            count = Task.query.filter_by(priority=priority).count()
            priority_counts[priority.value] = count
        
        # Tasks by type
        type_counts = {}
        for task_type in TaskType:
            count = Task.query.filter_by(task_type=task_type).count()
            type_counts[task_type.value] = count
        
        return jsonify({
            'status_counts': status_counts,
            'overdue_count': overdue_count,
            'due_today_count': due_today_count,
            'priority_counts': priority_counts,
            'type_counts': type_counts,
            'total_tasks': Task.query.count()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@task_bp.route('/tasks/calendar', methods=['GET'])
def get_task_calendar():
    """Get tasks for calendar view"""
    try:
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        
        if not start_date_str or not end_date_str:
            return jsonify({'error': 'start_date and end_date are required'}), 400
        
        try:
            start_date = datetime.fromisoformat(start_date_str)
            end_date = datetime.fromisoformat(end_date_str)
        except ValueError:
            return jsonify({'error': 'Invalid date format'}), 400
        
        tasks = Task.query.filter(
            Task.due_date >= start_date,
            Task.due_date <= end_date
        ).order_by(Task.due_date.asc()).all()
        
        return jsonify({
            'tasks': [task.to_dict() for task in tasks],
            'start_date': start_date_str,
            'end_date': end_date_str
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

