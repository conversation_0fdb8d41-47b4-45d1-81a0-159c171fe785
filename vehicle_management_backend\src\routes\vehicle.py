from flask import Blueprint, request, jsonify
from src.models.vehicle import Vehicle, VehicleStatus, VehicleStatusHistory, db
from src.models.user import User
from datetime import datetime, timedelta
import json

vehicle_bp = Blueprint('vehicle', __name__)

@vehicle_bp.route('/vehicles', methods=['GET'])
def get_vehicles():
    """Get all vehicles with optional filtering"""
    try:
        # Get query parameters for filtering
        make = request.args.get('make')
        model = request.args.get('model')
        status = request.args.get('status')
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)
        year = request.args.get('year', type=int)
        
        # Pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # Build query
        query = Vehicle.query
        
        if make:
            query = query.filter(Vehicle.make.ilike(f'%{make}%'))
        if model:
            query = query.filter(Vehicle.model.ilike(f'%{model}%'))
        if status:
            try:
                status_enum = VehicleStatus(status)
                query = query.filter(Vehicle.status == status_enum)
            except ValueError:
                return jsonify({'error': 'Invalid status value'}), 400
        if min_price:
            query = query.filter(Vehicle.price >= min_price)
        if max_price:
            query = query.filter(Vehicle.price <= max_price)
        if year:
            query = query.filter(Vehicle.year == year)
        
        # Execute query with pagination
        vehicles = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        return jsonify({
            'vehicles': [vehicle.to_dict() for vehicle in vehicles.items],
            'pagination': {
                'page': vehicles.page,
                'pages': vehicles.pages,
                'per_page': vehicles.per_page,
                'total': vehicles.total,
                'has_next': vehicles.has_next,
                'has_prev': vehicles.has_prev
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@vehicle_bp.route('/vehicles/<int:vehicle_id>', methods=['GET'])
def get_vehicle(vehicle_id):
    """Get a specific vehicle by ID"""
    try:
        vehicle = Vehicle.query.get_or_404(vehicle_id)
        return jsonify(vehicle.to_dict())
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@vehicle_bp.route('/vehicles', methods=['POST'])
def create_vehicle():
    """Create a new vehicle"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['vin', 'make', 'model', 'year', 'mileage', 'price', 
                          'fuel_type', 'transmission', 'body_type', 'color']
        
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Check if VIN already exists
        existing_vehicle = Vehicle.query.filter_by(vin=data['vin']).first()
        if existing_vehicle:
            return jsonify({'error': 'Vehicle with this VIN already exists'}), 400
        
        # Create new vehicle
        vehicle = Vehicle(
            vin=data['vin'],
            make=data['make'],
            model=data['model'],
            year=data['year'],
            mileage=data['mileage'],
            price=data['price'],
            fuel_type=data['fuel_type'],
            transmission=data['transmission'],
            body_type=data['body_type'],
            color=data['color'],
            engine_size=data.get('engine_size'),
            power_kw=data.get('power_kw'),
            power_hp=data.get('power_hp'),
            doors=data.get('doors'),
            seats=data.get('seats'),
            description=data.get('description'),
            features=json.dumps(data.get('features', [])),
            created_by=data.get('created_by'),
            assigned_to=data.get('assigned_to')
        )
        
        # Set status if provided
        if 'status' in data:
            try:
                vehicle.status = VehicleStatus(data['status'])
            except ValueError:
                return jsonify({'error': 'Invalid status value'}), 400
        
        db.session.add(vehicle)
        db.session.commit()
        
        # Create initial status history entry
        status_history = VehicleStatusHistory(
            vehicle_id=vehicle.id,
            new_status=vehicle.status,
            changed_by=data.get('created_by'),
            notes="Vehicle created"
        )
        db.session.add(status_history)
        db.session.commit()
        
        return jsonify(vehicle.to_dict()), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@vehicle_bp.route('/vehicles/<int:vehicle_id>', methods=['PUT'])
def update_vehicle(vehicle_id):
    """Update a vehicle"""
    try:
        vehicle = Vehicle.query.get_or_404(vehicle_id)
        data = request.get_json()
        
        # Store old status for history
        old_status = vehicle.status
        
        # Update fields
        updatable_fields = [
            'make', 'model', 'year', 'mileage', 'price', 'fuel_type',
            'transmission', 'body_type', 'color', 'engine_size', 'power_kw',
            'power_hp', 'doors', 'seats', 'description', 'assigned_to'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(vehicle, field, data[field])
        
        # Handle features as JSON
        if 'features' in data:
            vehicle.features = json.dumps(data['features'])
        
        # Handle status change
        if 'status' in data:
            try:
                new_status = VehicleStatus(data['status'])
                if new_status != old_status:
                    vehicle.status = new_status
                    
                    # Create status history entry
                    status_history = VehicleStatusHistory(
                        vehicle_id=vehicle.id,
                        old_status=old_status,
                        new_status=new_status,
                        changed_by=data.get('updated_by'),
                        notes=data.get('status_notes', 'Status updated')
                    )
                    db.session.add(status_history)
                    
            except ValueError:
                return jsonify({'error': 'Invalid status value'}), 400
        
        vehicle.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify(vehicle.to_dict())
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@vehicle_bp.route('/vehicles/<int:vehicle_id>', methods=['DELETE'])
def delete_vehicle(vehicle_id):
    """Delete a vehicle"""
    try:
        vehicle = Vehicle.query.get_or_404(vehicle_id)
        db.session.delete(vehicle)
        db.session.commit()
        
        return jsonify({'message': 'Vehicle deleted successfully'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@vehicle_bp.route('/vehicles/<int:vehicle_id>/status', methods=['PUT'])
def update_vehicle_status(vehicle_id):
    """Update vehicle status"""
    try:
        vehicle = Vehicle.query.get_or_404(vehicle_id)
        data = request.get_json()
        
        if 'status' not in data:
            return jsonify({'error': 'Status is required'}), 400
        
        try:
            old_status = vehicle.status
            new_status = VehicleStatus(data['status'])
            
            if new_status != old_status:
                vehicle.status = new_status
                vehicle.updated_at = datetime.utcnow()
                
                # Create status history entry
                status_history = VehicleStatusHistory(
                    vehicle_id=vehicle.id,
                    old_status=old_status,
                    new_status=new_status,
                    changed_by=data.get('changed_by'),
                    notes=data.get('notes', 'Status updated')
                )
                db.session.add(status_history)
                db.session.commit()
                
                return jsonify({
                    'message': 'Status updated successfully',
                    'vehicle': vehicle.to_dict()
                })
            else:
                return jsonify({'message': 'Status unchanged'})
                
        except ValueError:
            return jsonify({'error': 'Invalid status value'}), 400
            
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@vehicle_bp.route('/vehicles/<int:vehicle_id>/history', methods=['GET'])
def get_vehicle_history(vehicle_id):
    """Get vehicle status history"""
    try:
        vehicle = Vehicle.query.get_or_404(vehicle_id)
        history = VehicleStatusHistory.query.filter_by(vehicle_id=vehicle_id)\
                                          .order_by(VehicleStatusHistory.changed_at.desc())\
                                          .all()
        
        return jsonify({
            'vehicle_id': vehicle_id,
            'history': [entry.to_dict() for entry in history]
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@vehicle_bp.route('/vehicles/stats', methods=['GET'])
def get_vehicle_stats():
    """Get vehicle statistics"""
    try:
        total_vehicles = Vehicle.query.count()
        
        # Count by status
        status_counts = {}
        for status in VehicleStatus:
            count = Vehicle.query.filter_by(status=status).count()
            status_counts[status.value] = count
        
        # Average price
        avg_price = db.session.query(db.func.avg(Vehicle.price)).scalar() or 0
        
        # Recent additions (last 7 days)
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent_additions = Vehicle.query.filter(Vehicle.created_at >= week_ago).count()
        
        return jsonify({
            'total_vehicles': total_vehicles,
            'status_counts': status_counts,
            'average_price': round(avg_price, 2),
            'recent_additions': recent_additions
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

