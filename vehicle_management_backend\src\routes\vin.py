from flask import Blueprint, jsonify, request
from ..services.vin_scanner import vin_scanner

vin_bp = Blueprint('vin', __name__)

@vin_bp.route('/vin/validate', methods=['POST'])
def validate_vin():
    """Validate a VIN number"""
    try:
        data = request.get_json()
        if not data or 'vin' not in data:
            return jsonify({
                'success': False,
                'error': 'VIN ist erforderlich'
            }), 400
        
        vin = data['vin']
        result = vin_scanner.validate_vin(vin)
        
        return jsonify({
            'success': result.get('valid', False),
            'data': result
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@vin_bp.route('/vin/decode', methods=['POST'])
def decode_vin():
    """Decode VIN to extract vehicle information"""
    try:
        data = request.get_json()
        if not data or 'vin' not in data:
            return jsonify({
                'success': False,
                'error': 'VIN ist erforderlich'
            }), 400
        
        vin = data['vin']
        result = vin_scanner.decode_vin(vin)
        
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@vin_bp.route('/vin/scan', methods=['POST'])
def scan_vin():
    """Simulate VIN scanning from camera image"""
    try:
        # In a real implementation, this would handle image upload
        # For demo, we simulate the scanning process
        
        if 'image' not in request.files:
            return jsonify({
                'success': False,
                'error': 'Kein Bild hochgeladen'
            }), 400
        
        image_file = request.files['image']
        if image_file.filename == '':
            return jsonify({
                'success': False,
                'error': 'Keine Datei ausgewählt'
            }), 400
        
        # Read image data
        image_data = image_file.read()
        
        # Simulate VIN scanning
        result = vin_scanner.scan_vin_from_image(image_data)
        
        if result['success']:
            # If VIN was successfully scanned, also decode it
            decode_result = vin_scanner.decode_vin(result['vin'])
            if decode_result['success']:
                result['vehicle_data'] = decode_result['data']
        
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@vin_bp.route('/vin/history/<vin>', methods=['GET'])
def get_vehicle_history(vin):
    """Get vehicle history based on VIN"""
    try:
        result = vin_scanner.get_vehicle_history(vin)
        return jsonify(result)
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@vin_bp.route('/vin/lookup', methods=['POST'])
def lookup_vin():
    """Complete VIN lookup with validation, decoding, and history"""
    try:
        data = request.get_json()
        if not data or 'vin' not in data:
            return jsonify({
                'success': False,
                'error': 'VIN ist erforderlich'
            }), 400
        
        vin = data['vin']
        
        # Validate VIN
        validation = vin_scanner.validate_vin(vin)
        if not validation['valid']:
            return jsonify({
                'success': False,
                'error': validation['error']
            })
        
        # Decode VIN
        decode_result = vin_scanner.decode_vin(vin)
        if not decode_result['success']:
            return jsonify(decode_result)
        
        # Get history
        history_result = vin_scanner.get_vehicle_history(vin)
        
        return jsonify({
            'success': True,
            'data': {
                'validation': validation,
                'vehicle_info': decode_result['data'],
                'history': history_result.get('data', {})
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

